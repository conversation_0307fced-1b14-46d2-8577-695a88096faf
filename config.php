<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'admin_dashboard');

// Website settings
$website_settings = [
    'name' => 'My Website',
    'logo' => 'assets/logo.png',
    'primary_color' => '#3B82F6',
    'secondary_color' => '#1E40AF',
    'accent_color' => '#F59E0B'
];

// Database connection
function getConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch(PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }
}

// Create tables if they don't exist
function createTables() {
    $pdo = getConnection();
    
    // News table
    $sql = "CREATE TABLE IF NOT EXISTS news (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        status ENUM('draft', 'published') DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    
    // Settings table
    $sql = "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    
    // Insert default settings if not exist
    $settings = [
        ['website_name', 'My Website'],
        ['website_logo', 'assets/logo.png'],
        ['primary_color', '#3B82F6'],
        ['secondary_color', '#1E40AF'],
        ['accent_color', '#F59E0B']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value) VALUES (?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
}

// Initialize database
createTables();

// Helper functions
function getSetting($key) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? $result['setting_value'] : null;
}

function updateSetting($key, $value) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
    return $stmt->execute([$key, $value, $value]);
}

function getNews($limit = null) {
    $pdo = getConnection();
    $sql = "SELECT * FROM news ORDER BY created_at DESC";
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    $stmt = $pdo->query($sql);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addNews($title, $content, $status = 'draft') {
    $pdo = getConnection();
    $stmt = $pdo->prepare("INSERT INTO news (title, content, status) VALUES (?, ?, ?)");
    return $stmt->execute([$title, $content, $status]);
}

function updateNews($id, $title, $content, $status) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("UPDATE news SET title = ?, content = ?, status = ? WHERE id = ?");
    return $stmt->execute([$title, $content, $status, $id]);
}

function deleteNews($id) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("DELETE FROM news WHERE id = ?");
    return $stmt->execute([$id]);
}

function getNewsById($id) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT * FROM news WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Statistics functions
function getStats() {
    $pdo = getConnection();
    
    // Get news count
    $stmt = $pdo->query("SELECT COUNT(*) as total_news FROM news");
    $news_count = $stmt->fetch(PDO::FETCH_ASSOC)['total_news'];
    
    // Get published news count
    $stmt = $pdo->query("SELECT COUNT(*) as published_news FROM news WHERE status = 'published'");
    $published_count = $stmt->fetch(PDO::FETCH_ASSOC)['published_news'];
    
    return [
        'total_users' => 1234, // Mock data
        'total_news' => $news_count,
        'published_news' => $published_count,
        'page_views' => 12345, // Mock data
        'revenue' => 12345 // Mock data
    ];
}
?>
