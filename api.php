<?php
// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(E_ALL);

require_once 'config.php';

header('Content-Type: application/json');

// Function to send JSON response and exit
function sendJsonResponse($data) {
    echo json_encode($data);
    exit;
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';

// Wrap everything in try-catch to handle errors properly
try {
    switch ($action) {
        case 'get_stats':
            sendJsonResponse(getStats());
            break;
        
    case 'get_news':
        $limit = $_GET['limit'] ?? null;
        sendJsonResponse(getNews($limit));
        break;
        
    case 'add_news':
        $title = $_POST['title'] ?? '';
        $content = $_POST['content'] ?? '';
        $status = $_POST['status'] ?? 'draft';
        $image = null;

        if (empty($title) || empty($content)) {
            sendJsonResponse(['success' => false, 'message' => 'Title and content are required']);
        }

        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $file = $_FILES['image'];
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];

            if (!in_array($file['type'], $allowed_types)) {
                echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed']);
                break;
            }

            if ($file['size'] > 2 * 1024 * 1024) { // 2MB limit
                echo json_encode(['success' => false, 'message' => 'File size too large. Maximum 2MB allowed']);
                break;
            }

            // Create assets directory if it doesn't exist
            if (!file_exists('assets/news')) {
                mkdir('assets/news', 0777, true);
            }

            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'news_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
            $filepath = 'assets/news/' . $filename;

            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                $image = $filepath;
            }
        }

        $result = addNews($title, $content, $status, $image);
        sendJsonResponse(['success' => $result, 'message' => $result ? 'News added successfully' : 'Failed to add news']);
        
    case 'update_news':
        $id = $_POST['id'] ?? 0;
        $title = $_POST['title'] ?? '';
        $content = $_POST['content'] ?? '';
        $status = $_POST['status'] ?? 'draft';
        $image = null;

        if (empty($id) || empty($title) || empty($content)) {
            echo json_encode(['success' => false, 'message' => 'ID, title and content are required']);
            break;
        }

        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $file = $_FILES['image'];
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];

            if (!in_array($file['type'], $allowed_types)) {
                echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed']);
                break;
            }

            if ($file['size'] > 2 * 1024 * 1024) { // 2MB limit
                echo json_encode(['success' => false, 'message' => 'File size too large. Maximum 2MB allowed']);
                break;
            }

            // Create assets directory if it doesn't exist
            if (!file_exists('assets/news')) {
                mkdir('assets/news', 0777, true);
            }

            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'news_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
            $filepath = 'assets/news/' . $filename;

            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                $image = $filepath;

                // Delete old image if exists
                $oldNews = getNewsById($id);
                if ($oldNews && $oldNews['image'] && file_exists($oldNews['image'])) {
                    unlink($oldNews['image']);
                }
            }
        }

        $result = updateNews($id, $title, $content, $status, $image);
        echo json_encode(['success' => $result, 'message' => $result ? 'News updated successfully' : 'Failed to update news']);
        break;
        
    case 'delete_news':
        $id = $_POST['id'] ?? 0;
        
        if (empty($id)) {
            echo json_encode(['success' => false, 'message' => 'ID is required']);
            break;
        }
        
        $result = deleteNews($id);
        echo json_encode(['success' => $result, 'message' => $result ? 'News deleted successfully' : 'Failed to delete news']);
        break;
        
    case 'get_news_by_id':
        $id = $_GET['id'] ?? 0;
        
        if (empty($id)) {
            echo json_encode(['success' => false, 'message' => 'ID is required']);
            break;
        }
        
        $news = getNewsById($id);
        echo json_encode(['success' => $news !== false, 'data' => $news]);
        break;
        
    case 'update_settings':
        $website_name = $_POST['website_name'] ?? '';
        $primary_color = $_POST['primary_color'] ?? '';
        $secondary_color = $_POST['secondary_color'] ?? '';
        $accent_color = $_POST['accent_color'] ?? '';
        
        $success = true;
        $messages = [];
        
        if (!empty($website_name)) {
            if (!updateSetting('website_name', $website_name)) {
                $success = false;
                $messages[] = 'Failed to update website name';
            }
        }
        
        if (!empty($primary_color)) {
            if (!updateSetting('primary_color', $primary_color)) {
                $success = false;
                $messages[] = 'Failed to update primary color';
            }
        }
        
        if (!empty($secondary_color)) {
            if (!updateSetting('secondary_color', $secondary_color)) {
                $success = false;
                $messages[] = 'Failed to update secondary color';
            }
        }
        
        if (!empty($accent_color)) {
            if (!updateSetting('accent_color', $accent_color)) {
                $success = false;
                $messages[] = 'Failed to update accent color';
            }
        }
        
        echo json_encode([
            'success' => $success,
            'message' => $success ? 'Settings updated successfully' : implode(', ', $messages)
        ]);
        break;
        
    case 'get_settings':
        $settings = [
            'website_name' => getSetting('website_name'),
            'website_logo' => getSetting('website_logo'),
            'primary_color' => getSetting('primary_color'),
            'secondary_color' => getSetting('secondary_color'),
            'accent_color' => getSetting('accent_color')
        ];
        echo json_encode(['success' => true, 'data' => $settings]);
        break;
        
    case 'upload_logo':
        if (!isset($_FILES['logo'])) {
            echo json_encode(['success' => false, 'message' => 'No file uploaded']);
            break;
        }
        
        $file = $_FILES['logo'];
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        
        if (!in_array($file['type'], $allowed_types)) {
            echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed']);
            break;
        }
        
        if ($file['size'] > 2 * 1024 * 1024) { // 2MB limit
            echo json_encode(['success' => false, 'message' => 'File size too large. Maximum 2MB allowed']);
            break;
        }
        
        // Create assets directory if it doesn't exist
        if (!file_exists('assets')) {
            mkdir('assets', 0777, true);
        }
        
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'logo_' . time() . '.' . $extension;
        $filepath = 'assets/' . $filename;
        
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            updateSetting('website_logo', $filepath);
            echo json_encode(['success' => true, 'message' => 'Logo uploaded successfully', 'filepath' => $filepath]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to upload logo']);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}
?>
