<?php
require_once 'config.php';

// Get current settings
$settings = [
    'website_name' => getSetting('website_name') ?: 'My Website',
    'website_logo' => getSetting('website_logo') ?: 'assets/logo.png',
    'primary_color' => getSetting('primary_color') ?: '#3B82F6',
    'secondary_color' => getSetting('secondary_color') ?: '#1E40AF',
    'accent_color' => getSetting('accent_color') ?: '#F59E0B'
];

// Get statistics
$stats = getStats();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($settings['website_name']); ?> - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/custom.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '<?php echo $settings['primary_color']; ?>',
                        secondary: '<?php echo $settings['secondary_color']; ?>',
                        accent: '<?php echo $settings['accent_color']; ?>',
                        dark: '#1F2937',
                        light: '#F9FAFB'
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            --primary-color: <?php echo $settings['primary_color']; ?>;
            --secondary-color: <?php echo $settings['secondary_color']; ?>;
            --accent-color: <?php echo $settings['accent_color']; ?>;
        }
        .bg-primary { background-color: var(--primary-color) !important; }
        .text-primary { color: var(--primary-color) !important; }
        .border-primary { border-color: var(--primary-color) !important; }
        .hover\:bg-primary:hover { background-color: var(--primary-color) !important; }
        .hover\:text-primary:hover { color: var(--primary-color) !important; }
        .focus\:ring-primary:focus { --tw-ring-color: var(--primary-color) !important; }

        /* Additional responsive and UI improvements */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Smooth transitions */
        * {
            transition: all 0.2s ease-in-out;
        }

        /* Card hover effects */
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Better mobile sidebar */
        .w-70 {
            width: 17.5rem; /* 280px */
        }

        @media (max-width: 1023px) {
            #sidebar {
                backdrop-filter: blur(10px);
            }

            /* Larger mobile UI elements */
            .mobile-larger {
                font-size: 1.1rem;
                padding: 0.75rem 1rem;
            }

            .mobile-icon-larger {
                font-size: 1.25rem;
            }

            .mobile-button-larger {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
            }

            /* Larger mobile buttons and inputs */
            .mobile-input-larger {
                padding: 0.875rem 1rem;
                font-size: 1rem;
            }

            /* Larger mobile table elements */
            .mobile-table-larger td {
                padding: 1rem 0.75rem;
            }
        }

        /* Table row hover */
        tbody tr:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        /* Scrollable table with shadow indicators */
        .table-container {
            position: relative;
        }

        .table-container::before,
        .table-container::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 20px;
            pointer-events: none;
            z-index: 10;
            transition: opacity 0.3s ease;
        }

        .table-container::before {
            left: 0;
            background: linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0));
            opacity: 0;
        }

        .table-container::after {
            right: 0;
            background: linear-gradient(to left, rgba(255,255,255,1), rgba(255,255,255,0));
            opacity: 1;
        }

        .table-container.scrolled-left::before {
            opacity: 1;
        }

        .table-container.scrolled-right::after {
            opacity: 0;
        }

        /* Sticky column shadow */
        .sticky-shadow {
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }

        /* Mobile table improvements */
        @media (max-width: 768px) {
            .table-container {
                border-radius: 0.5rem;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .mobile-scroll-hint {
                display: block;
                text-align: center;
                padding: 0.5rem;
                background: #f3f4f6;
                color: #6b7280;
                font-size: 0.75rem;
                border-radius: 0.5rem 0.5rem 0 0;
            }

            /* Larger touch targets on mobile */
            .mobile-touch-target {
                min-height: 44px;
                min-width: 44px;
            }

            /* Smaller checkbox on mobile */
            .mobile-checkbox-small {
                width: 16px !important;
                height: 16px !important;
                min-width: 16px !important;
                min-height: 16px !important;
            }

            /* Ensure checkbox container has proper padding */
            .checkbox-container-mobile {
                padding: 0.5rem;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        @media (min-width: 769px) {
            .mobile-scroll-hint {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-70 lg:w-64 bg-white shadow-lg transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-center h-16 bg-primary text-white">
                <h1 class="text-xl font-bold">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    <?php echo htmlspecialchars($settings['website_name']); ?>
                </h1>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 px-4 py-6 space-y-2">
                <a href="#dashboard" onclick="showSection('dashboard')" class="nav-link flex items-center px-4 py-4 lg:py-3 text-gray-700 rounded-lg hover:bg-primary hover:text-white transition-colors duration-200 mobile-larger">
                    <i class="fas fa-home mr-3 w-5 mobile-icon-larger"></i>
                    Dashboard
                </a>
                <a href="#berita" onclick="showSection('berita')" class="nav-link flex items-center px-4 py-4 lg:py-3 text-gray-700 rounded-lg hover:bg-primary hover:text-white transition-colors duration-200 mobile-larger">
                    <i class="fas fa-newspaper mr-3 w-5 mobile-icon-larger"></i>
                    Berita
                </a>
                <a href="#pengaturan" onclick="showSection('pengaturan')" class="nav-link flex items-center px-4 py-4 lg:py-3 text-gray-700 rounded-lg hover:bg-primary hover:text-white transition-colors duration-200 mobile-larger">
                    <i class="fas fa-cog mr-3 w-5 mobile-icon-larger"></i>
                    Pengaturan
                </a>
            </nav>
        </div>

        <!-- Mobile menu button -->
        <div class="lg:hidden">
            <button id="mobile-menu-button" class="fixed top-4 left-4 z-50 p-2 rounded-md bg-primary text-white shadow-lg">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden lg:ml-0">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button id="mobile-menu-button-header" class="lg:hidden mr-4 p-2 rounded-md text-gray-600 hover:text-primary hover:bg-gray-100">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h2 id="page-title" class="text-2xl font-semibold text-gray-800">Dashboard</h2>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <button class="flex items-center text-gray-700 hover:text-primary transition-colors duration-200">
                                <i class="fas fa-user-circle text-2xl mr-2"></i>
                                <span class="hidden md:block">Admin</span>
                                <i class="fas fa-chevron-down ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="section-content p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Stats Cards -->
                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-blue-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-users text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Users</p>
                            <p class="text-2xl font-semibold text-gray-900"><?php echo number_format($stats['total_users']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-green-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-newspaper text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Berita</p>
                            <p class="text-2xl font-semibold text-gray-900"><?php echo number_format($stats['total_news']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-yellow-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-eye text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Page Views</p>
                            <p class="text-2xl font-semibold text-gray-900"><?php echo number_format($stats['page_views']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover border-l-4 border-red-500">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 text-red-600">
                            <i class="fas fa-chart-line text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Revenue</p>
                            <p class="text-2xl font-semibold text-gray-900">$<?php echo number_format($stats['revenue']); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Statistik Bulanan
                    </h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded">
                        <p class="text-gray-500">Chart akan ditampilkan di sini</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Distribusi Konten
                    </h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded">
                        <p class="text-gray-500">Pie chart akan ditampilkan di sini</p>
                    </div>
                </div>
            </div>
        </div>

                <!-- Berita Section -->
                <div id="berita-section" class="section-content p-6 hidden">
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i class="fas fa-newspaper mr-2"></i>
                            Manajemen Berita
                        </h3>
                        <div class="flex flex-row gap-2 justify-end">
                            <button id="delete-selected-btn" onclick="deleteSelectedNews()" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed mobile-button-larger text-sm lg:text-base" disabled>
                                <i class="fas fa-trash mr-1 lg:mr-2"></i>
                                <span class="hidden sm:inline">Hapus Terpilih</span>
                                <span class="sm:hidden">Hapus</span>
                                (<span id="selected-count">0</span>)
                            </button>
                            <button onclick="showAddBeritaModal()" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition-colors duration-200 mobile-button-larger text-sm lg:text-base">
                                <i class="fas fa-plus mr-1 lg:mr-2"></i>
                                <span class="hidden sm:inline">Tambah Berita</span>
                                <span class="sm:hidden">Tambah</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <div class="mobile-scroll-hint">
                            <i class="fas fa-arrows-alt-h mr-1"></i>
                            Geser tabel ke kiri/kanan untuk melihat semua kolom
                        </div>
                        <div class="table-container overflow-x-auto" id="table-container">
                            <table class="min-w-full divide-y divide-gray-200" style="min-width: 800px;">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-2 py-3 text-center w-12 sticky left-0 bg-gray-50 z-10">
                                            <div class="checkbox-container-mobile">
                                                <input type="checkbox" id="select-all" class="rounded border-gray-300 text-primary focus:ring-primary mobile-checkbox-small" onchange="toggleSelectAll()">
                                            </div>
                                        </th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">No</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Gambar</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-64">Judul & Deskripsi</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">Tanggal & Waktu</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="news-table-body" class="bg-white divide-y divide-gray-200">
                                    <!-- News will be loaded here via JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

                <!-- Pengaturan Section -->
                <div id="pengaturan-section" class="section-content p-6 hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Website Settings -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-globe mr-2"></i>
                        Pengaturan Website
                    </h3>
                    <form class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Nama Website</label>
                            <input type="text" id="website-name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary" value="<?php echo htmlspecialchars($settings['website_name']); ?>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Logo Website</label>
                            <div class="flex items-center space-x-4">
                                <div id="logo-preview" class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                                    <?php if (file_exists($settings['website_logo'])): ?>
                                        <img src="<?php echo $settings['website_logo']; ?>" alt="Logo" class="w-full h-full object-cover">
                                    <?php else: ?>
                                        <i class="fas fa-image text-gray-400"></i>
                                    <?php endif; ?>
                                </div>
                                <input type="file" id="logo-upload" accept="image/*" class="hidden">
                                <button type="button" onclick="document.getElementById('logo-upload').click()" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition-colors duration-200">
                                    Upload Logo
                                </button>
                            </div>
                        </div>
                        <button type="submit" onclick="saveWebsiteSettings()" class="w-full bg-primary text-white py-2 rounded-lg hover:bg-secondary transition-colors duration-200">
                            Simpan Pengaturan
                        </button>
                    </form>
                </div>

                <!-- Color Settings -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-palette mr-2"></i>
                        Pengaturan Warna
                    </h3>
                    <form class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Warna Primary</label>
                            <div class="flex items-center space-x-2">
                                <input type="color" id="primary-color" value="<?php echo $settings['primary_color']; ?>" class="w-12 h-10 border border-gray-300 rounded">
                                <input type="text" id="primary-color-text" value="<?php echo $settings['primary_color']; ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Warna Secondary</label>
                            <div class="flex items-center space-x-2">
                                <input type="color" id="secondary-color" value="<?php echo $settings['secondary_color']; ?>" class="w-12 h-10 border border-gray-300 rounded">
                                <input type="text" id="secondary-color-text" value="<?php echo $settings['secondary_color']; ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Warna Accent</label>
                            <div class="flex items-center space-x-2">
                                <input type="color" id="accent-color" value="<?php echo $settings['accent_color']; ?>" class="w-12 h-10 border border-gray-300 rounded">
                                <input type="text" id="accent-color-text" value="<?php echo $settings['accent_color']; ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>
                        </div>
                        <button type="submit" onclick="saveColorSettings()" class="w-full bg-primary text-white py-2 rounded-lg hover:bg-secondary transition-colors duration-200">
                            Terapkan Warna
                        </button>
                    </form>
                </div>
            </div>
            </main>
        </div>
    </div>

    <!-- Add Berita Modal -->
    <div id="addBeritaModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">Tambah Berita Baru</h3>
                </div>
                <div class="p-6">
                    <form id="news-form" class="space-y-4" enctype="multipart/form-data">
                        <input type="hidden" id="news-id" value="">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Judul Berita</label>
                            <input type="text" id="news-title" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Gambar Berita</label>
                            <div class="flex items-center space-x-4">
                                <div id="news-image-preview" class="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                                    <i class="fas fa-image text-gray-400"></i>
                                </div>
                                <div class="flex-1">
                                    <input type="file" id="news-image" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                    <p class="text-xs text-gray-500 mt-1">Format: JPG, PNG, GIF. Max: 2MB</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Konten</label>
                            <textarea rows="4" id="news-content" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary" required></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select id="news-status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="draft">Draft</option>
                                <option value="published">Published</option>
                            </select>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="hideAddBeritaModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200">
                                Batal
                            </button>
                            <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition-colors duration-200">
                                Simpan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenuButtonHeader = document.getElementById('mobile-menu-button-header');
        const sidebar = document.getElementById('sidebar');

        function toggleSidebar() {
            sidebar.classList.toggle('-translate-x-full');
        }

        if (mobileMenuButton) {
            mobileMenuButton.addEventListener('click', toggleSidebar);
        }

        if (mobileMenuButtonHeader) {
            mobileMenuButtonHeader.addEventListener('click', toggleSidebar);
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth < 1024 &&
                !sidebar.contains(e.target) &&
                !mobileMenuButton?.contains(e.target) &&
                !mobileMenuButtonHeader?.contains(e.target)) {
                sidebar.classList.add('-translate-x-full');
            }
        });

        // Section navigation
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section-content').forEach(section => {
                section.classList.add('hidden');
            });

            // Show selected section
            document.getElementById(sectionName + '-section').classList.remove('hidden');

            // Update page title
            const titles = {
                'dashboard': 'Dashboard',
                'berita': 'Manajemen Berita',
                'pengaturan': 'Pengaturan'
            };
            document.getElementById('page-title').textContent = titles[sectionName];

            // Update active nav link
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('bg-primary', 'text-white');
                link.classList.add('text-gray-700');
            });

            // Find and update the clicked nav link
            const navLink = document.querySelector(`a[href="#${sectionName}"]`);
            if (navLink) {
                navLink.classList.add('bg-primary', 'text-white');
                navLink.classList.remove('text-gray-700');
            }

            // Close mobile menu
            if (window.innerWidth < 1024) {
                sidebar.classList.add('-translate-x-full');
            }

            // Save current section to localStorage
            localStorage.setItem('currentSection', sectionName);

            // Load section-specific data
            if (sectionName === 'berita') {
                loadNews();
            }
        }

        // Load last visited section
        function loadLastSection() {
            const lastSection = localStorage.getItem('currentSection') || 'dashboard';

            // Show a brief indicator if returning to a saved section
            if (lastSection !== 'dashboard' && localStorage.getItem('currentSection')) {
                setTimeout(() => {
                    showNotification(`Kembali ke halaman ${lastSection === 'berita' ? 'Berita' : 'Pengaturan'}`, 'info');
                }, 500);
            }

            showSection(lastSection);
        }

        // Modal functions
        function showAddBeritaModal() {
            document.getElementById('addBeritaModal').classList.remove('hidden');
            document.getElementById('news-form').reset();
            document.getElementById('news-id').value = '';
            document.getElementById('news-image-preview').innerHTML = '<i class="fas fa-image text-gray-400"></i>';
            document.querySelector('#addBeritaModal h3').textContent = 'Tambah Berita Baru';

            // Save modal state
            localStorage.setItem('modalOpen', 'addBerita');
        }

        function hideAddBeritaModal() {
            document.getElementById('addBeritaModal').classList.add('hidden');

            // Clear modal state
            localStorage.removeItem('modalOpen');
        }

        // Restore modal state if needed
        function restoreModalState() {
            const modalOpen = localStorage.getItem('modalOpen');
            if (modalOpen === 'addBerita') {
                // Don't auto-open modal on refresh to avoid confusion
                localStorage.removeItem('modalOpen');
            }
        }

        // Close modal when clicking outside
        document.getElementById('addBeritaModal').addEventListener('click', (e) => {
            if (e.target.id === 'addBeritaModal') {
                hideAddBeritaModal();
            }
        });

        // Load news data
        function loadNews() {
            fetch('api.php?action=get_news')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('news-table-body');
                    tbody.innerHTML = '';

                    if (data.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-4 text-center text-gray-500">Belum ada berita</td></tr>';
                        return;
                    }

                    data.forEach((news, index) => {
                        const row = document.createElement('tr');
                        row.className = 'hover:bg-gray-50';
                        const statusClass = news.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
                        const statusText = news.status === 'published' ? 'Published' : 'Draft';
                        const createdDate = new Date(news.created_at);
                        const formattedDate = createdDate.toLocaleDateString('id-ID');
                        const formattedTime = createdDate.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });

                        row.innerHTML = `
                            <td class="px-2 py-4 sticky left-0 bg-white z-10">
                                <div class="checkbox-container-mobile">
                                    <input type="checkbox" class="news-checkbox rounded border-gray-300 text-primary focus:ring-primary mobile-checkbox-small" value="${news.id}" onchange="updateSelectedCount()">
                                </div>
                            </td>
                            <td class="px-3 py-4 text-sm text-gray-900 font-medium">${index + 1}</td>
                            <td class="px-3 py-4">
                                <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                                    ${news.image ? `<img src="${news.image}" alt="News Image" class="w-full h-full object-cover">` : '<i class="fas fa-image text-gray-400"></i>'}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900 mb-1">${news.title}</div>
                                <div class="text-sm text-gray-500 line-clamp-2">${news.content.substring(0, 100)}...</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="flex flex-col">
                                    <span class="flex items-center text-xs"><i class="fas fa-calendar mr-1 text-gray-400"></i>${formattedDate}</span>
                                    <span class="flex items-center mt-1 text-xs"><i class="fas fa-clock mr-1 text-gray-400"></i>${formattedTime}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">${statusText}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-1">
                                    <button onclick="viewNews(${news.id})" class="text-blue-600 hover:text-blue-900 p-1.5 rounded hover:bg-blue-50 transition-colors duration-200" title="Lihat">
                                        <i class="fas fa-eye text-sm"></i>
                                    </button>
                                    <button onclick="editNews(${news.id})" class="text-indigo-600 hover:text-indigo-900 p-1.5 rounded hover:bg-indigo-50 transition-colors duration-200" title="Edit">
                                        <i class="fas fa-edit text-sm"></i>
                                    </button>
                                    <button onclick="deleteNews(${news.id})" class="text-red-600 hover:text-red-900 p-1.5 rounded hover:bg-red-50 transition-colors duration-200" title="Hapus">
                                        <i class="fas fa-trash text-sm"></i>
                                    </button>
                                </div>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });

                    // Setup table scroll indicators
                    setupTableScrollIndicators();

                    // Restore selected checkboxes
                    setTimeout(() => {
                        restoreSelectedCheckboxes();
                    }, 100);
                })
                .catch(error => {
                    console.error('Error loading news:', error);
                    showNotification('Error loading news', 'error');
                });
        }

        // Setup table scroll indicators
        function setupTableScrollIndicators() {
            const tableContainer = document.getElementById('table-container');
            if (!tableContainer) return;

            function updateScrollIndicators() {
                const { scrollLeft, scrollWidth, clientWidth } = tableContainer;
                const container = tableContainer.closest('.table-container');
                const stickyColumns = tableContainer.querySelectorAll('.sticky');

                if (scrollLeft > 0) {
                    container.classList.add('scrolled-left');
                    stickyColumns.forEach(col => col.classList.add('sticky-shadow'));
                } else {
                    container.classList.remove('scrolled-left');
                    stickyColumns.forEach(col => col.classList.remove('sticky-shadow'));
                }

                if (scrollLeft + clientWidth >= scrollWidth - 1) {
                    container.classList.add('scrolled-right');
                } else {
                    container.classList.remove('scrolled-right');
                }

                // Save scroll position
                localStorage.setItem('tableScrollLeft', scrollLeft);
            }

            tableContainer.addEventListener('scroll', updateScrollIndicators);

            // Restore scroll position
            const savedScrollLeft = localStorage.getItem('tableScrollLeft');
            if (savedScrollLeft) {
                tableContainer.scrollLeft = parseInt(savedScrollLeft);
            }

            updateScrollIndicators(); // Initial check
        }

        // Edit news
        function editNews(id) {
            fetch(`api.php?action=get_news_by_id&id=${id}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const news = result.data;
                        document.getElementById('news-id').value = news.id;
                        document.getElementById('news-title').value = news.title;
                        document.getElementById('news-content').value = news.content;
                        document.getElementById('news-status').value = news.status;

                        // Show existing image if available
                        const imagePreview = document.getElementById('news-image-preview');
                        if (news.image) {
                            imagePreview.innerHTML = `<img src="${news.image}" alt="Current Image" class="w-full h-full object-cover">`;
                        } else {
                            imagePreview.innerHTML = '<i class="fas fa-image text-gray-400"></i>';
                        }

                        document.querySelector('#addBeritaModal h3').textContent = 'Edit Berita';
                        showAddBeritaModal();
                    } else {
                        showNotification(result.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading news:', error);
                    showNotification('Error loading news', 'error');
                });
        }

        // Delete news
        function deleteNews(id) {
            if (!confirm('Apakah Anda yakin ingin menghapus berita ini?')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'delete_news');
            formData.append('id', id);

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message, 'success');
                    loadNews();
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting news:', error);
                showNotification('Error deleting news', 'error');
            });
        }

        // Handle news form submission
        document.getElementById('news-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            const newsId = document.getElementById('news-id').value;
            const imageFile = document.getElementById('news-image').files[0];

            if (newsId) {
                formData.append('action', 'update_news');
                formData.append('id', newsId);
            } else {
                formData.append('action', 'add_news');
            }

            formData.append('title', document.getElementById('news-title').value);
            formData.append('content', document.getElementById('news-content').value);
            formData.append('status', document.getElementById('news-status').value);

            if (imageFile) {
                formData.append('image', imageFile);
            }

            // Show loading state
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Menyimpan...';
            submitBtn.disabled = true;

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message, 'success');
                    hideAddBeritaModal();
                    loadNews();
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error saving news:', error);
                showNotification('Error saving news', 'error');
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Checkbox functions
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.news-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateSelectedCount();
        }

        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('.news-checkbox:checked');
            const count = checkboxes.length;
            const selectAll = document.getElementById('select-all');
            const deleteBtn = document.getElementById('delete-selected-btn');
            const countSpan = document.getElementById('selected-count');

            countSpan.textContent = count;
            deleteBtn.disabled = count === 0;

            // Save selected items
            const selectedIds = Array.from(checkboxes).map(cb => cb.value);
            localStorage.setItem('selectedNewsIds', JSON.stringify(selectedIds));

            // Update select all checkbox state
            const allCheckboxes = document.querySelectorAll('.news-checkbox');
            if (count === 0) {
                selectAll.indeterminate = false;
                selectAll.checked = false;
            } else if (count === allCheckboxes.length) {
                selectAll.indeterminate = false;
                selectAll.checked = true;
            } else {
                selectAll.indeterminate = true;
                selectAll.checked = false;
            }
        }

        // Restore selected checkboxes
        function restoreSelectedCheckboxes() {
            const selectedIds = JSON.parse(localStorage.getItem('selectedNewsIds') || '[]');
            selectedIds.forEach(id => {
                const checkbox = document.querySelector(`.news-checkbox[value="${id}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
            updateSelectedCount();
        }

        // Delete selected news
        function deleteSelectedNews() {
            const checkboxes = document.querySelectorAll('.news-checkbox:checked');
            const ids = Array.from(checkboxes).map(cb => cb.value);

            if (ids.length === 0) {
                showNotification('Pilih berita yang ingin dihapus', 'error');
                return;
            }

            if (!confirm(`Apakah Anda yakin ingin menghapus ${ids.length} berita terpilih?`)) {
                return;
            }

            // Delete each selected news
            let completed = 0;
            let errors = 0;

            ids.forEach(id => {
                const formData = new FormData();
                formData.append('action', 'delete_news');
                formData.append('id', id);

                fetch('api.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(result => {
                    completed++;
                    if (!result.success) errors++;

                    if (completed === ids.length) {
                        if (errors === 0) {
                            showNotification(`${ids.length} berita berhasil dihapus`, 'success');
                        } else {
                            showNotification(`${ids.length - errors} berita berhasil dihapus, ${errors} gagal`, 'error');
                        }
                        // Clear selected items from localStorage
                        localStorage.removeItem('selectedNewsIds');
                        loadNews();
                        updateSelectedCount();
                    }
                })
                .catch(error => {
                    completed++;
                    errors++;
                    console.error('Error deleting news:', error);

                    if (completed === ids.length) {
                        showNotification(`${ids.length - errors} berita berhasil dihapus, ${errors} gagal`, 'error');
                        loadNews();
                        updateSelectedCount();
                    }
                });
            });
        }

        // View news function
        function viewNews(id) {
            fetch(`api.php?action=get_news_by_id&id=${id}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const news = result.data;
                        // Create a simple modal to view news
                        const modal = document.createElement('div');
                        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center p-4';
                        modal.innerHTML = `
                            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">Detail Berita</h3>
                                    <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="p-6">
                                    <h2 class="text-xl font-bold text-gray-900 mb-4">${news.title}</h2>
                                    ${news.image ? `<img src="${news.image}" alt="News Image" class="w-full h-48 object-cover rounded-lg mb-4">` : ''}
                                    <div class="text-gray-700 mb-4">${news.content}</div>
                                    <div class="flex items-center justify-between text-sm text-gray-500">
                                        <span>Status: <span class="px-2 py-1 rounded-full text-xs ${news.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">${news.status === 'published' ? 'Published' : 'Draft'}</span></span>
                                        <span>${new Date(news.created_at).toLocaleString('id-ID')}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(modal);

                        // Close modal when clicking outside
                        modal.addEventListener('click', (e) => {
                            if (e.target === modal) {
                                modal.remove();
                            }
                        });
                    } else {
                        showNotification(result.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading news:', error);
                    showNotification('Error loading news', 'error');
                });
        }

        // Handle image preview in form
        document.getElementById('news-image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('news-image-preview');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="w-full h-full object-cover">`;
                };
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = '<i class="fas fa-image text-gray-400"></i>';
            }
        });

        // Save website settings
        function saveWebsiteSettings() {
            const formData = new FormData();
            formData.append('action', 'update_settings');
            formData.append('website_name', document.getElementById('website-name').value);

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message, 'success');
                    // Update page title
                    document.title = document.getElementById('website-name').value + ' - Admin Dashboard';
                    document.querySelector('.sidebar h1').innerHTML = '<i class="fas fa-tachometer-alt mr-2"></i>' + document.getElementById('website-name').value;
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error saving settings:', error);
                showNotification('Error saving settings', 'error');
            });
        }

        // Save color settings
        function saveColorSettings() {
            const formData = new FormData();
            formData.append('action', 'update_settings');
            formData.append('primary_color', document.getElementById('primary-color').value);
            formData.append('secondary_color', document.getElementById('secondary-color').value);
            formData.append('accent_color', document.getElementById('accent-color').value);

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message + ' - Refresh halaman untuk melihat perubahan', 'success');
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error saving color settings:', error);
                showNotification('Error saving color settings', 'error');
            });
        }

        // Handle logo upload
        document.getElementById('logo-upload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('action', 'upload_logo');
            formData.append('logo', file);

            fetch('api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showNotification(result.message, 'success');
                    // Update logo preview
                    document.getElementById('logo-preview').innerHTML = `<img src="${result.filepath}" alt="Logo" class="w-full h-full object-cover">`;
                } else {
                    showNotification(result.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error uploading logo:', error);
                showNotification('Error uploading logo', 'error');
            });
        });

        // Color input synchronization
        document.getElementById('primary-color').addEventListener('change', function() {
            document.getElementById('primary-color-text').value = this.value;
        });
        document.getElementById('primary-color-text').addEventListener('change', function() {
            document.getElementById('primary-color').value = this.value;
        });

        document.getElementById('secondary-color').addEventListener('change', function() {
            document.getElementById('secondary-color-text').value = this.value;
        });
        document.getElementById('secondary-color-text').addEventListener('change', function() {
            document.getElementById('secondary-color').value = this.value;
        });

        document.getElementById('accent-color').addEventListener('change', function() {
            document.getElementById('accent-color-text').value = this.value;
        });
        document.getElementById('accent-color-text').addEventListener('change', function() {
            document.getElementById('accent-color').value = this.value;
        });

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'} mr-2"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto hide after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Load last visited section
            loadLastSection();

            // Restore modal state
            restoreModalState();

            // Load initial data for all sections
            loadNews();
        });
    </script>
</body>
</html>
